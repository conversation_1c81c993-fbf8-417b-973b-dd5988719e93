'use client'

import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { logOut } from '@/lib/auth'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  BookOpenIcon, 
  UserGroupIcon, 
  VideoCameraIcon, 
  DocumentTextIcon,
  AcademicCapIcon,
  PlusIcon,
  ArrowRightOnRectangleIcon 
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

export default function AdminDashboard() {
  const { user } = useAuth()
  const router = useRouter()

  const handleLogout = async () => {
    const { error } = await logOut()
    if (error) {
      toast.error('حدث خطأ أثناء تسجيل الخروج')
    } else {
      toast.success('تم تسجيل الخروج بنجاح')
      router.push('/')
    }
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div>
                <h1 className="text-2xl font-bold text-primary-900">
                  لوحة تحكم المدرس
                </h1>
                <p className="text-secondary-600">
                  مرحباً {user?.full_name || user?.displayName}
                </p>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center gap-2 px-4 py-2 text-secondary-600 hover:text-primary-600 transition-colors"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5" />
                تسجيل الخروج
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Link
              href="/admin/courses/create"
              className="bg-primary-600 hover:bg-primary-700 text-white rounded-lg p-6 transition-colors group"
            >
              <div className="flex items-center">
                <PlusIcon className="h-8 w-8" />
                <div className="mr-4">
                  <h3 className="text-lg font-semibold">إنشاء كورس جديد</h3>
                  <p className="text-primary-100">أضف كورس تعليمي جديد</p>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/students"
              className="bg-green-600 hover:bg-green-700 text-white rounded-lg p-6 transition-colors group"
            >
              <div className="flex items-center">
                <UserGroupIcon className="h-8 w-8" />
                <div className="mr-4">
                  <h3 className="text-lg font-semibold">إدارة الطلاب</h3>
                  <p className="text-green-100">عرض وإدارة الطلاب</p>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/certificates"
              className="bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg p-6 transition-colors group"
            >
              <div className="flex items-center">
                <AcademicCapIcon className="h-8 w-8" />
                <div className="mr-4">
                  <h3 className="text-lg font-semibold">إدارة الشهادات</h3>
                  <p className="text-yellow-100">إنشاء وإدارة الشهادات</p>
                </div>
              </div>
            </Link>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <BookOpenIcon className="h-8 w-8 text-primary-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-secondary-600">إجمالي الكورسات</p>
                  <p className="text-2xl font-bold text-primary-900">0</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <UserGroupIcon className="h-8 w-8 text-green-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-secondary-600">الطلاب المسجلين</p>
                  <p className="text-2xl font-bold text-green-900">0</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <VideoCameraIcon className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-secondary-600">إجمالي الفيديوهات</p>
                  <p className="text-2xl font-bold text-blue-900">0</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <AcademicCapIcon className="h-8 w-8 text-yellow-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-secondary-600">الشهادات الصادرة</p>
                  <p className="text-2xl font-bold text-yellow-900">0</p>
                </div>
              </div>
            </div>
          </div>

          {/* My Courses Section */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-semibold text-primary-900">كورساتي</h2>
              <Link
                href="/admin/courses/create"
                className="btn-primary text-sm"
              >
                <PlusIcon className="h-4 w-4 ml-2" />
                إضافة كورس
              </Link>
            </div>
            <div className="p-6">
              <div className="text-center py-12">
                <BookOpenIcon className="mx-auto h-12 w-12 text-secondary-400" />
                <h3 className="mt-2 text-sm font-medium text-secondary-900">
                  لا توجد كورسات
                </h3>
                <p className="mt-1 text-sm text-secondary-500">
                  ابدأ بإنشاء كورسك الأول
                </p>
                <div className="mt-6">
                  <Link
                    href="/admin/courses/create"
                    className="btn-primary"
                  >
                    <PlusIcon className="h-4 w-4 ml-2" />
                    إنشاء كورس جديد
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity Section */}
          <div className="mt-8 bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-primary-900">النشاط الأخير</h2>
            </div>
            <div className="p-6">
              <div className="text-center py-12">
                <div className="mx-auto h-12 w-12 bg-secondary-100 rounded-full flex items-center justify-center">
                  <DocumentTextIcon className="h-6 w-6 text-secondary-400" />
                </div>
                <h3 className="mt-2 text-sm font-medium text-secondary-900">
                  لا يوجد نشاط حديث
                </h3>
                <p className="mt-1 text-sm text-secondary-500">
                  ابدأ بإنشاء الكورسات لرؤية نشاطك هنا
                </p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
