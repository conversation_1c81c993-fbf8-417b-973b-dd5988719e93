'use client'

import { useState } from 'react'
import Link from 'next/link'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  BookOpenIcon, 
  PlayIcon, 
  ClockIcon,
  CheckCircleIcon,
  EyeIcon
} from '@heroicons/react/24/outline'

// Mock data - will be replaced with real data from database
const mockCourses = [
  {
    id: '1',
    title: 'مقدمة في البرمجة',
    description: 'تعلم أساسيات البرمجة باستخدام JavaScript',
    thumbnail: null,
    instructor: 'أحمد محمد',
    progress: 75,
    totalVideos: 12,
    watchedVideos: 9,
    totalDuration: 7200, // 2 hours in seconds
    enrolledAt: '2024-01-15',
    lastWatched: '2024-01-25',
    isCompleted: false
  },
  {
    id: '2',
    title: 'تطوير المواقع الإلكترونية',
    description: 'دورة شاملة في تطوير المواقع باستخدام React',
    thumbnail: null,
    instructor: 'فاطمة أحمد',
    progress: 100,
    totalVideos: 15,
    watchedVideos: 15,
    totalDuration: 9000, // 2.5 hours in seconds
    enrolledAt: '2024-01-10',
    lastWatched: '2024-01-22',
    isCompleted: true
  },
  {
    id: '3',
    title: 'تصميم واجهات المستخدم',
    description: 'تعلم تصميم واجهات جذابة وسهلة الاستخدام',
    thumbnail: null,
    instructor: 'محمد علي',
    progress: 25,
    totalVideos: 10,
    watchedVideos: 2,
    totalDuration: 6000, // 1.67 hours in seconds
    enrolledAt: '2024-01-20',
    lastWatched: '2024-01-23',
    isCompleted: false
  }
]

export default function StudentCoursesPage() {
  const [courses] = useState(mockCourses)
  const [filter, setFilter] = useState<'all' | 'in-progress' | 'completed'>('all')
  const [searchTerm, setSearchTerm] = useState('')

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.instructor.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filter === 'all' || 
                         (filter === 'completed' && course.isCompleted) ||
                         (filter === 'in-progress' && !course.isCompleted)
    
    return matchesSearch && matchesFilter
  })

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return hours > 0 ? `${hours}س ${minutes}د` : `${minutes}د`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  return (
    <ProtectedRoute requiredRole="student">
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-primary-900">
              كورساتي
            </h1>
            <p className="text-secondary-600">
              تابع تقدمك في الكورسات المسجلة
            </p>
          </div>

          {/* Filters and Search */}
          <Card className="mb-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="البحث في الكورسات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="form-input"
                />
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => setFilter('all')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    filter === 'all'
                      ? 'bg-primary-600 text-white'
                      : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'
                  }`}
                >
                  جميع الكورسات
                </button>
                <button
                  onClick={() => setFilter('in-progress')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    filter === 'in-progress'
                      ? 'bg-primary-600 text-white'
                      : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'
                  }`}
                >
                  قيد التقدم
                </button>
                <button
                  onClick={() => setFilter('completed')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    filter === 'completed'
                      ? 'bg-primary-600 text-white'
                      : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'
                  }`}
                >
                  مكتملة
                </button>
              </div>
            </div>
          </Card>

          {/* Courses Grid */}
          {filteredCourses.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCourses.map((course) => (
                <Card key={course.id} className="card-hover">
                  {/* Course Thumbnail */}
                  <div className="aspect-video bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg mb-4 flex items-center justify-center relative">
                    {course.thumbnail ? (
                      <img 
                        src={course.thumbnail} 
                        alt={course.title}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <BookOpenIcon className="h-12 w-12 text-primary-400" />
                    )}
                    
                    {/* Completion Badge */}
                    {course.isCompleted && (
                      <div className="absolute top-2 right-2 bg-green-500 text-white p-1 rounded-full">
                        <CheckCircleIcon className="h-4 w-4" />
                      </div>
                    )}
                  </div>

                  {/* Course Info */}
                  <div className="space-y-3">
                    <div>
                      <h3 className="text-lg font-semibold text-primary-900 line-clamp-2">
                        {course.title}
                      </h3>
                      <p className="text-sm text-secondary-600">
                        المدرس: {course.instructor}
                      </p>
                    </div>

                    <p className="text-secondary-600 text-sm line-clamp-2">
                      {course.description}
                    </p>

                    {/* Progress Bar */}
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm text-secondary-600">التقدم</span>
                        <span className="text-sm font-medium text-primary-600">
                          {course.progress}%
                        </span>
                      </div>
                      <div className="progress-bar">
                        <div 
                          className="progress-bar-fill"
                          style={{ width: `${course.progress}%` }}
                        />
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-sm text-secondary-500">
                      <div className="flex items-center gap-1">
                        <PlayIcon className="h-4 w-4" />
                        <span>{course.watchedVideos}/{course.totalVideos} فيديو</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ClockIcon className="h-4 w-4" />
                        <span>{formatDuration(course.totalDuration)}</span>
                      </div>
                    </div>

                    {/* Last Activity */}
                    <p className="text-xs text-secondary-500">
                      آخر مشاهدة: {formatDate(course.lastWatched)}
                    </p>

                    {/* Actions */}
                    <div className="flex gap-2 pt-3 border-t">
                      <Link href={`/student/courses/${course.id}`} className="flex-1">
                        <Button variant="primary" size="sm" className="w-full">
                          {course.isCompleted ? (
                            <>
                              <EyeIcon className="h-4 w-4 ml-1" />
                              مراجعة
                            </>
                          ) : (
                            <>
                              <PlayIcon className="h-4 w-4 ml-1" />
                              متابعة
                            </>
                          )}
                        </Button>
                      </Link>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            /* Empty State */
            <Card>
              <div className="text-center py-12">
                <BookOpenIcon className="mx-auto h-12 w-12 text-secondary-400" />
                <h3 className="mt-2 text-sm font-medium text-secondary-900">
                  {searchTerm ? 'لا توجد نتائج' : 'لا توجد كورسات'}
                </h3>
                <p className="mt-1 text-sm text-secondary-500">
                  {searchTerm 
                    ? 'جرب البحث بكلمات مختلفة' 
                    : 'لم يتم تسجيلك في أي كورس بعد'
                  }
                </p>
              </div>
            </Card>
          )}
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
