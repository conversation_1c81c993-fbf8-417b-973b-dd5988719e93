import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface User {
  id: string
  email: string
  full_name: string
  role: 'student' | 'admin'
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface Course {
  id: string
  title: string
  description: string
  thumbnail_url?: string
  instructor_id: string
  created_at: string
  updated_at: string
  is_published: boolean
}

export interface CourseSection {
  id: string
  course_id: string
  title: string
  description?: string
  order_index: number
  created_at: string
}

export interface Video {
  id: string
  section_id: string
  title: string
  description?: string
  video_url: string
  duration?: number
  order_index: number
  created_at: string
}

export interface PDF {
  id: string
  video_id: string
  title: string
  file_url: string
  file_size: number
  created_at: string
}

export interface Quiz {
  id: string
  course_id: string
  title: string
  description?: string
  questions: QuizQuestion[]
  passing_score: number
  created_at: string
}

export interface QuizQuestion {
  id: string
  question: string
  options: string[]
  correct_answer: number
  explanation?: string
}

export interface Certificate {
  id: string
  user_id: string
  course_id: string
  certificate_url: string
  issued_at: string
}

export interface StudentCourse {
  id: string
  student_id: string
  course_id: string
  enrolled_at: string
  progress: number
  completed_at?: string
}

export interface VideoProgress {
  id: string
  user_id: string
  video_id: string
  watched_duration: number
  completed: boolean
  last_watched_at: string
}

export interface QuizAttempt {
  id: string
  user_id: string
  quiz_id: string
  score: number
  answers: Record<string, number>
  completed_at: string
}
