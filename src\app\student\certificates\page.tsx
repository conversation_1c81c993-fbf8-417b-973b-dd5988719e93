'use client'

import { useState } from 'react'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  AcademicCapIcon, 
  ArrowDownTrayIcon,
  EyeIcon,
  ShareIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

// Mock data - will be replaced with real data from database
const mockCertificates = [
  {
    id: '1',
    courseName: 'تطوير المواقع الإلكترونية',
    instructor: 'فاطمة أحمد',
    issuedAt: '2024-01-22',
    certificateUrl: '/certificates/cert-1.pdf',
    grade: 95,
    completionDate: '2024-01-20'
  },
  {
    id: '2',
    courseName: 'أساسيات التصميم الجرافيكي',
    instructor: 'محمد علي',
    issuedAt: '2024-01-15',
    certificateUrl: '/certificates/cert-2.pdf',
    grade: 88,
    completionDate: '2024-01-12'
  }
]

const mockInProgressCourses = [
  {
    id: '1',
    title: 'مقدمة في البرمجة',
    progress: 75,
    instructor: 'أحمد محمد'
  },
  {
    id: '3',
    title: 'تصميم واجهات المستخدم',
    progress: 25,
    instructor: 'محمد علي'
  }
]

export default function StudentCertificatesPage() {
  const [certificates] = useState(mockCertificates)
  const [inProgressCourses] = useState(mockInProgressCourses)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredCertificates = certificates.filter(cert =>
    cert.courseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.instructor.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  const handleDownloadCertificate = (certificate: any) => {
    // TODO: Implement certificate download logic
    console.log('Downloading certificate:', certificate.id)
  }

  const handleViewCertificate = (certificate: any) => {
    // TODO: Implement certificate preview logic
    console.log('Viewing certificate:', certificate.id)
  }

  const handleShareCertificate = (certificate: any) => {
    // TODO: Implement certificate sharing logic
    console.log('Sharing certificate:', certificate.id)
  }

  return (
    <ProtectedRoute requiredRole="student">
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-primary-900">
              شهاداتي
            </h1>
            <p className="text-secondary-600">
              عرض وإدارة الشهادات التي حصلت عليها
            </p>
          </div>

          {/* Search */}
          <Card className="mb-6">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400" />
              <input
                type="text"
                placeholder="البحث في الشهادات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pr-10"
              />
            </div>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Certificates */}
            <div className="lg:col-span-2">
              <Card>
                <Card.Header>
                  <Card.Title>الشهادات المحصلة ({filteredCertificates.length})</Card.Title>
                </Card.Header>
                <Card.Content>
                  {filteredCertificates.length > 0 ? (
                    <div className="space-y-4">
                      {filteredCertificates.map((certificate) => (
                        <div key={certificate.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-4">
                              {/* Certificate Icon */}
                              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <AcademicCapIcon className="h-6 w-6 text-yellow-600" />
                              </div>

                              {/* Certificate Info */}
                              <div className="flex-1">
                                <h3 className="text-lg font-semibold text-primary-900 mb-1">
                                  {certificate.courseName}
                                </h3>
                                <p className="text-secondary-600 text-sm mb-2">
                                  المدرس: {certificate.instructor}
                                </p>
                                <div className="flex items-center gap-4 text-sm text-secondary-500">
                                  <span>تاريخ الإكمال: {formatDate(certificate.completionDate)}</span>
                                  <span>الدرجة: {certificate.grade}%</span>
                                  <span>تاريخ الإصدار: {formatDate(certificate.issuedAt)}</span>
                                </div>
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="flex gap-2 flex-shrink-0">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewCertificate(certificate)}
                              >
                                <EyeIcon className="h-4 w-4 ml-1" />
                                معاينة
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDownloadCertificate(certificate)}
                              >
                                <ArrowDownTrayIcon className="h-4 w-4 ml-1" />
                                تحميل
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleShareCertificate(certificate)}
                              >
                                <ShareIcon className="h-4 w-4 ml-1" />
                                مشاركة
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <AcademicCapIcon className="mx-auto h-12 w-12 text-secondary-400" />
                      <h3 className="mt-2 text-sm font-medium text-secondary-900">
                        {searchTerm ? 'لا توجد نتائج' : 'لا توجد شهادات'}
                      </h3>
                      <p className="mt-1 text-sm text-secondary-500">
                        {searchTerm 
                          ? 'جرب البحث بكلمات مختلفة' 
                          : 'أكمل الكورسات للحصول على شهادات'
                        }
                      </p>
                    </div>
                  )}
                </Card.Content>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Stats */}
              <Card>
                <Card.Header>
                  <Card.Title>إحصائياتي</Card.Title>
                </Card.Header>
                <Card.Content>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-secondary-600">الشهادات المحصلة</span>
                      <span className="font-semibold text-primary-900">{certificates.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-secondary-600">الكورسات قيد التقدم</span>
                      <span className="font-semibold text-primary-900">{inProgressCourses.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-secondary-600">متوسط الدرجات</span>
                      <span className="font-semibold text-primary-900">
                        {certificates.length > 0 
                          ? Math.round(certificates.reduce((sum, cert) => sum + cert.grade, 0) / certificates.length)
                          : 0
                        }%
                      </span>
                    </div>
                  </div>
                </Card.Content>
              </Card>

              {/* In Progress Courses */}
              <Card>
                <Card.Header>
                  <Card.Title>كورسات قيد التقدم</Card.Title>
                </Card.Header>
                <Card.Content>
                  {inProgressCourses.length > 0 ? (
                    <div className="space-y-3">
                      {inProgressCourses.map((course) => (
                        <div key={course.id} className="p-3 bg-secondary-50 rounded-lg">
                          <h4 className="font-medium text-secondary-900 mb-1">
                            {course.title}
                          </h4>
                          <p className="text-sm text-secondary-600 mb-2">
                            المدرس: {course.instructor}
                          </p>
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-xs text-secondary-600">التقدم</span>
                            <span className="text-xs font-medium text-primary-600">
                              {course.progress}%
                            </span>
                          </div>
                          <div className="progress-bar h-1">
                            <div 
                              className="progress-bar-fill h-1"
                              style={{ width: `${course.progress}%` }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-sm text-secondary-500">
                        لا توجد كورسات قيد التقدم
                      </p>
                    </div>
                  )}
                </Card.Content>
              </Card>

              {/* Achievement Badge */}
              <Card>
                <Card.Header>
                  <Card.Title>إنجازاتي</Card.Title>
                </Card.Header>
                <Card.Content>
                  <div className="text-center">
                    <div className="h-16 w-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-3">
                      <AcademicCapIcon className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="font-semibold text-primary-900 mb-1">
                      طالب متميز
                    </h4>
                    <p className="text-sm text-secondary-600">
                      حصلت على {certificates.length} شهادة
                    </p>
                  </div>
                </Card.Content>
              </Card>
            </div>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
