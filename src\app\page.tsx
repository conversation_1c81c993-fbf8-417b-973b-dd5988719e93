import Link from 'next/link'
import { BookOpenIcon, UserGroupIcon, AcademicCapIcon } from '@heroicons/react/24/outline'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-white">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-primary-900 mb-6">
            منصة الكورسات الإلكترونية
          </h1>
          <p className="text-xl text-secondary-600 mb-8 max-w-2xl mx-auto">
            منصة تعليمية احترافية تربط الطلاب بالمدرسين الخبراء.
            احصل على كورسات عالية الجودة، تتبع تقدمك، واحصل على شهادات معتمدة.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/login?role=student"
              className="btn-primary text-lg px-8 py-3"
            >
              دخول الطلاب
            </Link>
            <Link
              href="/auth/login?role=admin"
              className="btn-secondary text-lg px-8 py-3"
            >
              دخول المدرسين
            </Link>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center p-6 bg-white rounded-xl shadow-lg">
            <BookOpenIcon className="h-12 w-12 text-primary-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-primary-900 mb-2">
              محتوى تعليمي غني
            </h3>
            <p className="text-secondary-600">
              احصل على محاضرات فيديو، مواد PDF، واختبارات تفاعلية
            </p>
          </div>

          <div className="text-center p-6 bg-white rounded-xl shadow-lg">
            <UserGroupIcon className="h-12 w-12 text-primary-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-primary-900 mb-2">
              مدرسون خبراء
            </h3>
            <p className="text-secondary-600">
              تعلم من المحترفين في الصناعة والمعلمين ذوي الخبرة
            </p>
          </div>

          <div className="text-center p-6 bg-white rounded-xl shadow-lg">
            <AcademicCapIcon className="h-12 w-12 text-primary-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-primary-900 mb-2">
              احصل على شهادات
            </h3>
            <p className="text-secondary-600">
              أكمل الكورسات واحصل على شهادات احترافية معتمدة
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
