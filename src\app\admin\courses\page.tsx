'use client'

import { useState } from 'react'
import Link from 'next/link'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  PlusIcon, 
  BookOpenIcon, 
  EyeIcon, 
  PencilIcon, 
  TrashIcon,
  UserGroupIcon,
  VideoCameraIcon
} from '@heroicons/react/24/outline'

// Mock data - will be replaced with real data from database
const mockCourses = [
  {
    id: '1',
    title: 'مقدمة في البرمجة',
    description: 'تعلم أساسيات البرمجة باستخدام JavaScript',
    thumbnail: null,
    isPublished: true,
    studentsCount: 25,
    videosCount: 12,
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    title: 'تطوير المواقع الإلكترونية',
    description: 'دورة شاملة في تطوير المواقع باستخدام React',
    thumbnail: null,
    isPublished: false,
    studentsCount: 0,
    videosCount: 8,
    createdAt: '2024-01-20'
  }
]

export default function CoursesPage() {
  const [courses] = useState(mockCourses)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredCourses = courses.filter(course =>
    course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    course.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-2xl font-bold text-primary-900">
                إدارة الكورسات
              </h1>
              <p className="text-secondary-600">
                أنشئ وأدر كورساتك التعليمية
              </p>
            </div>
            <Link href="/admin/courses/create">
              <Button>
                <PlusIcon className="h-4 w-4 ml-2" />
                إنشاء كورس جديد
              </Button>
            </Link>
          </div>

          {/* Search and Filters */}
          <Card className="mb-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="البحث في الكورسات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="form-input"
                />
              </div>
              <div className="flex gap-2">
                <select className="form-input">
                  <option value="">جميع الكورسات</option>
                  <option value="published">المنشورة</option>
                  <option value="draft">المسودات</option>
                </select>
                <select className="form-input">
                  <option value="">ترتيب حسب</option>
                  <option value="newest">الأحدث</option>
                  <option value="oldest">الأقدم</option>
                  <option value="name">الاسم</option>
                </select>
              </div>
            </div>
          </Card>

          {/* Courses Grid */}
          {filteredCourses.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCourses.map((course) => (
                <Card key={course.id} className="card-hover">
                  {/* Course Thumbnail */}
                  <div className="aspect-video bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg mb-4 flex items-center justify-center">
                    {course.thumbnail ? (
                      <img 
                        src={course.thumbnail} 
                        alt={course.title}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <BookOpenIcon className="h-12 w-12 text-primary-400" />
                    )}
                  </div>

                  {/* Course Info */}
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <h3 className="text-lg font-semibold text-primary-900 line-clamp-2">
                        {course.title}
                      </h3>
                      <span className={`badge ${course.isPublished ? 'badge-success' : 'badge-warning'}`}>
                        {course.isPublished ? 'منشور' : 'مسودة'}
                      </span>
                    </div>

                    <p className="text-secondary-600 text-sm line-clamp-2">
                      {course.description}
                    </p>

                    {/* Stats */}
                    <div className="flex items-center gap-4 text-sm text-secondary-500">
                      <div className="flex items-center gap-1">
                        <UserGroupIcon className="h-4 w-4" />
                        <span>{course.studentsCount} طالب</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <VideoCameraIcon className="h-4 w-4" />
                        <span>{course.videosCount} فيديو</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-3 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                      >
                        <EyeIcon className="h-4 w-4 ml-1" />
                        عرض
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                      >
                        <PencilIcon className="h-4 w-4 ml-1" />
                        تعديل
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            /* Empty State */
            <Card>
              <div className="text-center py-12">
                <BookOpenIcon className="mx-auto h-12 w-12 text-secondary-400" />
                <h3 className="mt-2 text-sm font-medium text-secondary-900">
                  {searchTerm ? 'لا توجد نتائج' : 'لا توجد كورسات'}
                </h3>
                <p className="mt-1 text-sm text-secondary-500">
                  {searchTerm 
                    ? 'جرب البحث بكلمات مختلفة' 
                    : 'ابدأ بإنشاء كورسك الأول'
                  }
                </p>
                {!searchTerm && (
                  <div className="mt-6">
                    <Link href="/admin/courses/create">
                      <Button>
                        <PlusIcon className="h-4 w-4 ml-2" />
                        إنشاء كورس جديد
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </Card>
          )}
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
