# منصة الكورسات الإلكترونية

منصة تعليمية احترافية تربط الطلاب بالمدرسين الخبراء. احصل على كورسات عالية الجودة، تتبع تقدمك، واحصل على شهادات معتمدة.

## المميزات الرئيسية

### للطلاب
- 📚 عرض الكورسات المسجلة
- 🎥 مشغل فيديو متقدم مع تتبع التقدم
- 📄 تحميل المواد التعليمية (PDF)
- 📊 تتبع التقدم في الكورسات
- 🏆 عرض الشهادات المحصلة
- 📱 واجهة متجاوبة لجميع الأجهزة

### للمدرسين
- ➕ إنشاء وإدارة الكورسات
- 🎬 رفع الفيديوهات والمواد التعليمية
- 👥 إدارة الطلاب وتعيين الكورسات
- 📝 إنشاء الاختبارات والامتحانات
- 🎓 إصدار الشهادات
- 📈 تتبع أداء الطلاب

## التقنيات المستخدمة

- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS
- **Backend**: Node.js + Express.js
- **Database**: Firebase Firestore + Supabase PostgreSQL
- **Authentication**: Firebase Auth
- **Storage**: Supabase Storage
- **Hosting**: Firebase Hosting

## متطلبات النظام

- Node.js 18+ 
- npm أو yarn
- حساب Firebase
- حساب Supabase

## الإعداد والتشغيل

### 1. استنساخ المشروع

```bash
git clone <repository-url>
cd online-courses-platform
```

### 2. تثبيت التبعيات

```bash
npm install
```

### 3. إعداد Firebase

1. انتقل إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد
3. فعّل Authentication وFirestore Database
4. احصل على إعدادات المشروع من Project Settings

### 4. إعداد Supabase

1. انتقل إلى [Supabase](https://supabase.com/)
2. أنشئ مشروع جديد
3. احصل على URL و API Keys من Settings > API

### 5. إعداد متغيرات البيئة

انسخ ملف `.env.local.example` إلى `.env.local` وأضف القيم الصحيحة:

```bash
cp .env.local.example .env.local
```

املأ الملف بالقيم الصحيحة:

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 6. إعداد قاعدة البيانات

قم بتشغيل سكريبت إعداد قاعدة البيانات:

```bash
npm run setup-db
```

### 7. تشغيل المشروع

```bash
npm run dev
```

المشروع سيعمل على `http://localhost:3000`

## البناء والنشر

### بناء المشروع للإنتاج

```bash
npm run build
```

### النشر على Firebase Hosting

1. تثبيت Firebase CLI:

```bash
npm install -g firebase-tools
```

2. تسجيل الدخول إلى Firebase:

```bash
firebase login
```

3. تهيئة Firebase Hosting:

```bash
firebase init hosting
```

4. النشر:

```bash
npm run deploy
```

## هيكل المشروع

```
src/
├── app/                    # صفحات Next.js App Router
│   ├── auth/              # صفحات المصادقة
│   ├── admin/             # لوحة تحكم المدرس
│   ├── student/           # لوحة تحكم الطالب
│   └── globals.css        # الأنماط العامة
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── ui/               # مكونات واجهة المستخدم
│   └── layout/           # مكونات التخطيط
├── contexts/             # React Contexts
├── lib/                  # المكتبات والأدوات المساعدة
│   ├── firebase.ts      # إعداد Firebase
│   ├── supabase.ts      # إعداد Supabase
│   └── auth.ts          # وظائف المصادقة
└── scripts/             # سكريبتات الإعداد
```

## الاستخدام

### للطلاب

1. سجل حساب جديد كطالب
2. تصفح الكورسات المتاحة
3. شاهد الفيديوهات وحمل المواد
4. تتبع تقدمك واحصل على الشهادات

### للمدرسين

1. سجل حساب جديد كمدرس
2. أنشئ كورسات جديدة
3. ارفع الفيديوهات والمواد التعليمية
4. أدر الطلاب وأصدر الشهادات

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود واختبره
4. أرسل Pull Request

## الدعم

إذا واجهت أي مشاكل أو لديك أسئلة، يرجى فتح issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.
