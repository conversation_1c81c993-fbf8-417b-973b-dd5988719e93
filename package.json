{"name": "online-courses-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "export": "next build && next export", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "setup-db": "ts-node scripts/setup-database.ts", "deploy": "npm run build && firebase deploy --only hosting", "deploy-functions": "firebase deploy --only functions", "deploy-all": "firebase deploy"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "firebase": "^10.7.2", "@supabase/supabase-js": "^2.38.5", "@supabase/storage-js": "^2.5.5", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "react-player": "^2.13.0", "react-dropzone": "^14.2.3", "date-fns": "^2.30.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.4", "@types/uuid": "^9.0.7", "uuid": "^9.0.1", "ts-node": "^10.9.1", "firebase-tools": "^13.0.0"}}