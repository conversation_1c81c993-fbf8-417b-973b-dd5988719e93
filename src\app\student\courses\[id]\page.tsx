'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  ArrowLeftIcon,
  PlayIcon,
  PauseIcon,
  DocumentArrowDownIcon,
  CheckCircleIcon,
  ClockIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline'

// Mock data - will be replaced with real data from database
const mockCourse = {
  id: '1',
  title: 'مقدمة في البرمجة',
  description: 'تعلم أساسيات البرمجة باستخدام JavaScript',
  instructor: 'أحمد محمد',
  progress: 75,
  sections: [
    {
      id: '1',
      title: 'الأساسيات',
      videos: [
        {
          id: '1',
          title: 'مقدمة عن البرمجة',
          duration: 600, // 10 minutes
          videoUrl: '/videos/intro.mp4',
          isWatched: true,
          watchedDuration: 600,
          pdfs: [
            { id: '1', title: 'ملاحظات الدرس الأول', fileUrl: '/pdfs/lesson1.pdf', fileSize: 1024000 }
          ]
        },
        {
          id: '2',
          title: 'إعداد بيئة التطوير',
          duration: 900, // 15 minutes
          videoUrl: '/videos/setup.mp4',
          isWatched: true,
          watchedDuration: 900,
          pdfs: []
        },
        {
          id: '3',
          title: 'المتغيرات والثوابت',
          duration: 720, // 12 minutes
          videoUrl: '/videos/variables.mp4',
          isWatched: false,
          watchedDuration: 300,
          pdfs: [
            { id: '2', title: 'أمثلة على المتغيرات', fileUrl: '/pdfs/variables.pdf', fileSize: 512000 }
          ]
        }
      ]
    },
    {
      id: '2',
      title: 'البرمجة المتقدمة',
      videos: [
        {
          id: '4',
          title: 'الدوال والوظائف',
          duration: 1080, // 18 minutes
          videoUrl: '/videos/functions.mp4',
          isWatched: false,
          watchedDuration: 0,
          pdfs: []
        }
      ]
    }
  ]
}

export default function StudentCourseDetailsPage({ params }: { params: { id: string } }) {
  const [course] = useState(mockCourse)
  const [currentVideo, setCurrentVideo] = useState(course.sections[0]?.videos[0] || null)
  const [isPlaying, setIsPlaying] = useState(false)
  const router = useRouter()

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getVideoProgress = (video: any) => {
    return video.duration > 0 ? (video.watchedDuration / video.duration) * 100 : 0
  }

  const handleVideoSelect = (video: any) => {
    setCurrentVideo(video)
    setIsPlaying(false)
  }

  const handleDownloadPdf = (pdf: any) => {
    // TODO: Implement PDF download logic
    console.log('Downloading PDF:', pdf.title)
  }

  return (
    <ProtectedRoute requiredRole="student">
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="mr-4"
              >
                <ArrowLeftIcon className="h-4 w-4 ml-2" />
                رجوع
              </Button>
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-primary-900">
                  {course.title}
                </h1>
                <p className="text-secondary-600">
                  المدرس: {course.instructor}
                </p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-secondary-700">تقدم الكورس</span>
                <span className="text-sm font-medium text-primary-600">{course.progress}%</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-bar-fill"
                  style={{ width: `${course.progress}%` }}
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Video Player */}
            <div className="lg:col-span-2">
              <Card padding="none">
                {currentVideo ? (
                  <div>
                    {/* Video Player */}
                    <div className="video-player relative">
                      <div className="absolute inset-0 bg-black flex items-center justify-center">
                        <div className="text-center text-white">
                          <div className="mb-4">
                            <PlayIcon className="h-16 w-16 mx-auto opacity-75" />
                          </div>
                          <p className="text-lg font-medium">{currentVideo.title}</p>
                          <p className="text-sm opacity-75">
                            المدة: {formatDuration(currentVideo.duration)}
                          </p>
                          <Button
                            variant="primary"
                            className="mt-4"
                            onClick={() => setIsPlaying(!isPlaying)}
                          >
                            {isPlaying ? (
                              <>
                                <PauseIcon className="h-4 w-4 ml-2" />
                                إيقاف
                              </>
                            ) : (
                              <>
                                <PlayIcon className="h-4 w-4 ml-2" />
                                تشغيل
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Video Info */}
                    <div className="p-6">
                      <h2 className="text-xl font-semibold text-primary-900 mb-2">
                        {currentVideo.title}
                      </h2>
                      
                      {/* Video Progress */}
                      <div className="mb-4">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm text-secondary-600">تقدم الفيديو</span>
                          <span className="text-sm font-medium text-primary-600">
                            {Math.round(getVideoProgress(currentVideo))}%
                          </span>
                        </div>
                        <div className="progress-bar">
                          <div 
                            className="progress-bar-fill"
                            style={{ width: `${getVideoProgress(currentVideo)}%` }}
                          />
                        </div>
                      </div>

                      {/* PDFs */}
                      {currentVideo.pdfs && currentVideo.pdfs.length > 0 && (
                        <div>
                          <h3 className="text-lg font-medium text-primary-900 mb-3">
                            المواد التعليمية
                          </h3>
                          <div className="space-y-2">
                            {currentVideo.pdfs.map((pdf) => (
                              <div key={pdf.id} className="flex items-center justify-between bg-secondary-50 p-3 rounded-lg">
                                <div className="flex items-center gap-3">
                                  <div className="h-8 w-8 bg-red-100 rounded flex items-center justify-center">
                                    <BookOpenIcon className="h-4 w-4 text-red-600" />
                                  </div>
                                  <div>
                                    <p className="font-medium text-secondary-900">{pdf.title}</p>
                                    <p className="text-sm text-secondary-500">
                                      {formatFileSize(pdf.fileSize)}
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDownloadPdf(pdf)}
                                >
                                  <DocumentArrowDownIcon className="h-4 w-4 ml-1" />
                                  تحميل
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="p-12 text-center">
                    <PlayIcon className="h-12 w-12 mx-auto mb-4 text-secondary-400" />
                    <h3 className="text-lg font-medium text-secondary-900 mb-2">
                      اختر فيديو للمشاهدة
                    </h3>
                    <p className="text-secondary-500">
                      اختر فيديو من القائمة الجانبية لبدء المشاهدة
                    </p>
                  </div>
                )}
              </Card>
            </div>

            {/* Course Content */}
            <div className="space-y-4">
              <Card>
                <Card.Header>
                  <Card.Title>محتوى الكورس</Card.Title>
                </Card.Header>
                <Card.Content className="space-y-4">
                  {course.sections.map((section) => (
                    <div key={section.id}>
                      <h4 className="font-medium text-primary-900 mb-2">
                        {section.title}
                      </h4>
                      <div className="space-y-2">
                        {section.videos.map((video) => (
                          <button
                            key={video.id}
                            onClick={() => handleVideoSelect(video)}
                            className={`w-full text-right p-3 rounded-lg border transition-colors ${
                              currentVideo?.id === video.id
                                ? 'border-primary-300 bg-primary-50'
                                : 'border-secondary-200 hover:border-secondary-300 hover:bg-secondary-50'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                {video.isWatched ? (
                                  <CheckCircleIcon className="h-4 w-4 text-green-500" />
                                ) : (
                                  <div className="h-4 w-4 border-2 border-secondary-300 rounded-full" />
                                )}
                                <span className="text-sm font-medium text-secondary-900">
                                  {video.title}
                                </span>
                              </div>
                              <div className="flex items-center gap-1 text-xs text-secondary-500">
                                <ClockIcon className="h-3 w-3" />
                                {formatDuration(video.duration)}
                              </div>
                            </div>
                            
                            {/* Video Progress */}
                            {video.watchedDuration > 0 && (
                              <div className="mt-2">
                                <div className="progress-bar h-1">
                                  <div 
                                    className="progress-bar-fill h-1"
                                    style={{ width: `${getVideoProgress(video)}%` }}
                                  />
                                </div>
                              </div>
                            )}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </Card.Content>
              </Card>
            </div>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
