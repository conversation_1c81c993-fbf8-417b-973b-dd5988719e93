'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import FileUpload from '@/components/ui/FileUpload'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

interface CourseFormData {
  title: string
  description: string
  thumbnail: File | null
}

export default function CreateCoursePage() {
  const [formData, setFormData] = useState<CourseFormData>({
    title: '',
    description: '',
    thumbnail: null
  })
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleThumbnailSelect = (files: File[]) => {
    if (files.length > 0) {
      setFormData(prev => ({
        ...prev,
        thumbnail: files[0]
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validation
      if (!formData.title.trim()) {
        toast.error('يرجى إدخال عنوان الكورس')
        return
      }

      if (!formData.description.trim()) {
        toast.error('يرجى إدخال وصف الكورس')
        return
      }

      // TODO: Implement course creation logic
      // This will be implemented when we add the course management functionality
      
      toast.success('تم إنشاء الكورس بنجاح!')
      router.push('/admin/dashboard')
      
    } catch (error) {
      toast.error('حدث خطأ أثناء إنشاء الكورس')
    } finally {
      setLoading(false)
    }
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="mr-4"
              >
                <ArrowLeftIcon className="h-4 w-4 ml-2" />
                رجوع
              </Button>
              <h1 className="text-2xl font-bold text-primary-900">
                إنشاء كورس جديد
              </h1>
            </div>
            <p className="text-secondary-600">
              أنشئ كورساً تعليمياً جديداً وأضف المحتوى التعليمي
            </p>
          </div>

          {/* Form */}
          <Card>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Course Title */}
              <div>
                <Input
                  label="عنوان الكورس *"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="أدخل عنوان الكورس"
                  required
                />
              </div>

              {/* Course Description */}
              <div>
                <label className="form-label">
                  وصف الكورس *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="form-input"
                  placeholder="أدخل وصفاً مفصلاً للكورس"
                  required
                />
              </div>

              {/* Course Thumbnail */}
              <div>
                <FileUpload
                  label="صورة الكورس (اختيارية)"
                  description="اختر صورة تمثل الكورس (PNG, JPG, WEBP - حتى 5MB)"
                  accept={{
                    'image/*': ['.png', '.jpg', '.jpeg', '.webp']
                  }}
                  maxSize={5 * 1024 * 1024} // 5MB
                  onFileSelect={handleThumbnailSelect}
                />
              </div>

              {/* Course Settings */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-primary-900 mb-4">
                  إعدادات الكورس
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="mr-2 text-sm text-secondary-700">
                        نشر الكورس فوراً
                      </span>
                    </label>
                    <p className="text-xs text-secondary-500 mt-1">
                      يمكن للطلاب رؤية الكورس والتسجيل فيه
                    </p>
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="mr-2 text-sm text-secondary-700">
                        إرسال إشعارات للطلاب
                      </span>
                    </label>
                    <p className="text-xs text-secondary-500 mt-1">
                      إشعار الطلاب المسجلين بالكورس الجديد
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-4 space-x-reverse pt-6 border-t">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => router.back()}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  loading={loading}
                >
                  إنشاء الكورس
                </Button>
              </div>
            </form>
          </Card>

          {/* Next Steps Info */}
          <Card className="mt-8">
            <Card.Header>
              <Card.Title>الخطوات التالية</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="space-y-3 text-sm text-secondary-600">
                <p>• بعد إنشاء الكورس، يمكنك إضافة الأقسام والفيديوهات</p>
                <p>• رفع ملفات PDF كمواد تعليمية مساعدة</p>
                <p>• إنشاء اختبارات لتقييم الطلاب</p>
                <p>• تعيين الطلاب للكورس</p>
                <p>• إصدار شهادات للطلاب المتفوقين</p>
              </div>
            </Card.Content>
          </Card>
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
