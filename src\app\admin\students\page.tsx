'use client'

import { useState } from 'react'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Modal from '@/components/ui/Modal'
import { 
  UserGroupIcon, 
  PlusIcon, 
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  BookOpenIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline'

// Mock data - will be replaced with real data from database
const mockStudents = [
  {
    id: '1',
    fullName: 'أحمد محمد علي',
    email: '<EMAIL>',
    enrolledCourses: 3,
    completedCourses: 1,
    certificates: 1,
    joinedAt: '2024-01-10',
    lastActive: '2024-01-25'
  },
  {
    id: '2',
    fullName: 'فاطمة أحمد حسن',
    email: '<EMAIL>',
    enrolledCourses: 2,
    completedCourses: 2,
    certificates: 2,
    joinedAt: '2024-01-15',
    lastActive: '2024-01-24'
  },
  {
    id: '3',
    fullName: 'محمد عبدالله سالم',
    email: '<EMAIL>',
    enrolledCourses: 1,
    completedCourses: 0,
    certificates: 0,
    joinedAt: '2024-01-20',
    lastActive: '2024-01-23'
  }
]

const mockCourses = [
  { id: '1', title: 'مقدمة في البرمجة' },
  { id: '2', title: 'تطوير المواقع الإلكترونية' },
  { id: '3', title: 'تصميم واجهات المستخدم' }
]

export default function StudentsPage() {
  const [students] = useState(mockStudents)
  const [courses] = useState(mockCourses)
  const [searchTerm, setSearchTerm] = useState('')
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<string | null>(null)
  const [selectedCourse, setSelectedCourse] = useState('')

  const filteredStudents = students.filter(student =>
    student.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAssignCourse = () => {
    if (selectedStudent && selectedCourse) {
      // TODO: Implement course assignment logic
      console.log('Assigning course', selectedCourse, 'to student', selectedStudent)
      setIsAssignModalOpen(false)
      setSelectedStudent(null)
      setSelectedCourse('')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-2xl font-bold text-primary-900">
                إدارة الطلاب
              </h1>
              <p className="text-secondary-600">
                عرض وإدارة الطلاب المسجلين في المنصة
              </p>
            </div>
            <Button
              onClick={() => setIsAssignModalOpen(true)}
            >
              <PlusIcon className="h-4 w-4 ml-2" />
              تعيين كورس
            </Button>
          </div>

          {/* Search */}
          <Card className="mb-6">
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400" />
                <input
                  type="text"
                  placeholder="البحث عن طالب..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="form-input pr-10"
                />
              </div>
              <select className="form-input">
                <option value="">جميع الطلاب</option>
                <option value="active">النشطين</option>
                <option value="inactive">غير النشطين</option>
              </select>
            </div>
          </Card>

          {/* Students List */}
          {filteredStudents.length > 0 ? (
            <div className="space-y-4">
              {filteredStudents.map((student) => (
                <Card key={student.id}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      {/* Avatar */}
                      <div className="h-12 w-12 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 font-semibold">
                          {student.fullName.charAt(0)}
                        </span>
                      </div>

                      {/* Student Info */}
                      <div>
                        <h3 className="text-lg font-semibold text-primary-900">
                          {student.fullName}
                        </h3>
                        <p className="text-secondary-600 text-sm">
                          {student.email}
                        </p>
                        <p className="text-secondary-500 text-xs">
                          انضم في {formatDate(student.joinedAt)} • آخر نشاط {formatDate(student.lastActive)}
                        </p>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center gap-6">
                      <div className="text-center">
                        <div className="flex items-center gap-1 text-primary-600">
                          <BookOpenIcon className="h-4 w-4" />
                          <span className="font-semibold">{student.enrolledCourses}</span>
                        </div>
                        <p className="text-xs text-secondary-500">كورسات مسجلة</p>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center gap-1 text-green-600">
                          <BookOpenIcon className="h-4 w-4" />
                          <span className="font-semibold">{student.completedCourses}</span>
                        </div>
                        <p className="text-xs text-secondary-500">كورسات مكتملة</p>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center gap-1 text-yellow-600">
                          <AcademicCapIcon className="h-4 w-4" />
                          <span className="font-semibold">{student.certificates}</span>
                        </div>
                        <p className="text-xs text-secondary-500">شهادات</p>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <EyeIcon className="h-4 w-4 ml-1" />
                          عرض
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedStudent(student.id)
                            setIsAssignModalOpen(true)
                          }}
                        >
                          <PlusIcon className="h-4 w-4 ml-1" />
                          تعيين كورس
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            /* Empty State */
            <Card>
              <div className="text-center py-12">
                <UserGroupIcon className="mx-auto h-12 w-12 text-secondary-400" />
                <h3 className="mt-2 text-sm font-medium text-secondary-900">
                  {searchTerm ? 'لا توجد نتائج' : 'لا يوجد طلاب'}
                </h3>
                <p className="mt-1 text-sm text-secondary-500">
                  {searchTerm 
                    ? 'جرب البحث بكلمات مختلفة' 
                    : 'لم يسجل أي طالب في المنصة بعد'
                  }
                </p>
              </div>
            </Card>
          )}
        </div>

        {/* Assign Course Modal */}
        <Modal
          isOpen={isAssignModalOpen}
          onClose={() => {
            setIsAssignModalOpen(false)
            setSelectedStudent(null)
            setSelectedCourse('')
          }}
          title="تعيين كورس للطالب"
        >
          <div className="space-y-4">
            <div>
              <label className="form-label">اختر الطالب</label>
              <select
                value={selectedStudent || ''}
                onChange={(e) => setSelectedStudent(e.target.value)}
                className="form-input"
              >
                <option value="">اختر طالب...</option>
                {students.map((student) => (
                  <option key={student.id} value={student.id}>
                    {student.fullName}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="form-label">اختر الكورس</label>
              <select
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                className="form-input"
              >
                <option value="">اختر كورس...</option>
                {courses.map((course) => (
                  <option key={course.id} value={course.id}>
                    {course.title}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => {
                  setIsAssignModalOpen(false)
                  setSelectedStudent(null)
                  setSelectedCourse('')
                }}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleAssignCourse}
                disabled={!selectedStudent || !selectedCourse}
              >
                تعيين الكورس
              </Button>
            </div>
          </div>
        </Modal>
      </Layout>
    </ProtectedRoute>
  )
}
