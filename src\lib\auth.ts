import { 
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  updateProfile
} from 'firebase/auth'
import { doc, setDoc, getDoc } from 'firebase/firestore'
import { auth, db } from './firebase'
import { supabase } from './supabase'
import type { User } from './supabase'

export interface AuthUser extends FirebaseUser {
  role?: 'student' | 'admin'
  full_name?: string
}

// Sign up new user
export const signUp = async (
  email: string, 
  password: string, 
  fullName: string, 
  role: 'student' | 'admin'
) => {
  try {
    // Create user in Firebase Auth
    const { user } = await createUserWithEmailAndPassword(auth, email, password)
    
    // Update Firebase profile
    await updateProfile(user, {
      displayName: fullName
    })
    
    // Create user profile in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      email,
      full_name: fullName,
      role,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    
    // Create user profile in Supabase
    const { error: supabaseError } = await supabase
      .from('users')
      .insert({
        id: user.uid,
        email,
        full_name: fullName,
        role
      })
    
    if (supabaseError) {
      console.error('Error creating Supabase user:', supabaseError)
    }
    
    return { user, error: null }
  } catch (error: any) {
    return { user: null, error: error.message }
  }
}

// Sign in user
export const signIn = async (email: string, password: string) => {
  try {
    const { user } = await signInWithEmailAndPassword(auth, email, password)
    return { user, error: null }
  } catch (error: any) {
    return { user: null, error: error.message }
  }
}

// Sign out user
export const logOut = async () => {
  try {
    await signOut(auth)
    return { error: null }
  } catch (error: any) {
    return { error: error.message }
  }
}

// Get user profile
export const getUserProfile = async (uid: string): Promise<User | null> => {
  try {
    const docRef = doc(db, 'users', uid)
    const docSnap = await getDoc(docRef)
    
    if (docSnap.exists()) {
      return { id: uid, ...docSnap.data() } as User
    }
    return null
  } catch (error) {
    console.error('Error getting user profile:', error)
    return null
  }
}

// Auth state observer
export const onAuthStateChange = (callback: (user: AuthUser | null) => void) => {
  return onAuthStateChanged(auth, async (user) => {
    if (user) {
      const profile = await getUserProfile(user.uid)
      const authUser: AuthUser = {
        ...user,
        role: profile?.role,
        full_name: profile?.full_name
      }
      callback(authUser)
    } else {
      callback(null)
    }
  })
}

// Check if user is admin
export const isAdmin = (user: AuthUser | null): boolean => {
  return user?.role === 'admin'
}

// Check if user is student
export const isStudent = (user: AuthUser | null): boolean => {
  return user?.role === 'student'
}
