'use client'

import { useState } from 'react'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Modal from '@/components/ui/Modal'
import { 
  AcademicCapIcon, 
  PlusIcon, 
  EyeIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

// Mock data - will be replaced with real data from database
const mockCertificates = [
  {
    id: '1',
    studentName: 'أحمد محمد علي',
    studentEmail: '<EMAIL>',
    courseName: 'مقدمة في البرمجة',
    issuedAt: '2024-01-20',
    certificateUrl: '/certificates/cert-1.pdf'
  },
  {
    id: '2',
    studentName: 'فاطمة أحمد حسن',
    studentEmail: '<EMAIL>',
    courseName: 'تطوير المواقع الإلكترونية',
    issuedAt: '2024-01-22',
    certificateUrl: '/certificates/cert-2.pdf'
  }
]

const mockStudents = [
  { id: '1', name: 'أحمد محمد علي', email: '<EMAIL>' },
  { id: '2', name: 'فاطمة أحمد حسن', email: '<EMAIL>' },
  { id: '3', name: 'محمد عبدالله سالم', email: '<EMAIL>' }
]

const mockCourses = [
  { id: '1', title: 'مقدمة في البرمجة' },
  { id: '2', title: 'تطوير المواقع الإلكترونية' },
  { id: '3', title: 'تصميم واجهات المستخدم' }
]

export default function CertificatesPage() {
  const [certificates] = useState(mockCertificates)
  const [students] = useState(mockStudents)
  const [courses] = useState(mockCourses)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState('')
  const [selectedCourse, setSelectedCourse] = useState('')

  const filteredCertificates = certificates.filter(cert =>
    cert.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.courseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.studentEmail.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreateCertificate = () => {
    if (selectedStudent && selectedCourse) {
      // TODO: Implement certificate creation logic
      console.log('Creating certificate for student', selectedStudent, 'and course', selectedCourse)
      setIsCreateModalOpen(false)
      setSelectedStudent('')
      setSelectedCourse('')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-2xl font-bold text-primary-900">
                إدارة الشهادات
              </h1>
              <p className="text-secondary-600">
                إنشاء وإدارة شهادات إتمام الكورسات
              </p>
            </div>
            <Button
              onClick={() => setIsCreateModalOpen(true)}
            >
              <PlusIcon className="h-4 w-4 ml-2" />
              إنشاء شهادة جديدة
            </Button>
          </div>

          {/* Search */}
          <Card className="mb-6">
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400" />
                <input
                  type="text"
                  placeholder="البحث في الشهادات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="form-input pr-10"
                />
              </div>
              <select className="form-input">
                <option value="">جميع الكورسات</option>
                {courses.map((course) => (
                  <option key={course.id} value={course.id}>
                    {course.title}
                  </option>
                ))}
              </select>
            </div>
          </Card>

          {/* Certificates List */}
          {filteredCertificates.length > 0 ? (
            <div className="space-y-4">
              {filteredCertificates.map((certificate) => (
                <Card key={certificate.id}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      {/* Certificate Icon */}
                      <div className="h-12 w-12 bg-yellow-100 rounded-full flex items-center justify-center">
                        <AcademicCapIcon className="h-6 w-6 text-yellow-600" />
                      </div>

                      {/* Certificate Info */}
                      <div>
                        <h3 className="text-lg font-semibold text-primary-900">
                          {certificate.courseName}
                        </h3>
                        <p className="text-secondary-600">
                          الطالب: {certificate.studentName}
                        </p>
                        <p className="text-secondary-500 text-sm">
                          {certificate.studentEmail} • صدرت في {formatDate(certificate.issuedAt)}
                        </p>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <EyeIcon className="h-4 w-4 ml-1" />
                        معاينة
                      </Button>
                      <Button variant="outline" size="sm">
                        <ArrowDownTrayIcon className="h-4 w-4 ml-1" />
                        تحميل
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            /* Empty State */
            <Card>
              <div className="text-center py-12">
                <AcademicCapIcon className="mx-auto h-12 w-12 text-secondary-400" />
                <h3 className="mt-2 text-sm font-medium text-secondary-900">
                  {searchTerm ? 'لا توجد نتائج' : 'لا توجد شهادات'}
                </h3>
                <p className="mt-1 text-sm text-secondary-500">
                  {searchTerm 
                    ? 'جرب البحث بكلمات مختلفة' 
                    : 'لم يتم إصدار أي شهادات بعد'
                  }
                </p>
                {!searchTerm && (
                  <div className="mt-6">
                    <Button onClick={() => setIsCreateModalOpen(true)}>
                      <PlusIcon className="h-4 w-4 ml-2" />
                      إنشاء شهادة جديدة
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          )}
        </div>

        {/* Create Certificate Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => {
            setIsCreateModalOpen(false)
            setSelectedStudent('')
            setSelectedCourse('')
          }}
          title="إنشاء شهادة جديدة"
        >
          <div className="space-y-4">
            <div>
              <label className="form-label">اختر الطالب</label>
              <select
                value={selectedStudent}
                onChange={(e) => setSelectedStudent(e.target.value)}
                className="form-input"
              >
                <option value="">اختر طالب...</option>
                {students.map((student) => (
                  <option key={student.id} value={student.id}>
                    {student.name} ({student.email})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="form-label">اختر الكورس</label>
              <select
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                className="form-input"
              >
                <option value="">اختر كورس...</option>
                {courses.map((course) => (
                  <option key={course.id} value={course.id}>
                    {course.title}
                  </option>
                ))}
              </select>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                معلومات الشهادة
              </h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• سيتم إنشاء الشهادة تلقائياً بتاريخ اليوم</li>
                <li>• سيتم إرسال نسخة للطالب عبر البريد الإلكتروني</li>
                <li>• يمكن للطالب تحميل الشهادة من لوحة التحكم الخاصة به</li>
              </ul>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => {
                  setIsCreateModalOpen(false)
                  setSelectedStudent('')
                  setSelectedCourse('')
                }}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleCreateCertificate}
                disabled={!selectedStudent || !selectedCourse}
              >
                إنشاء الشهادة
              </Button>
            </div>
          </div>
        </Modal>
      </Layout>
    </ProtectedRoute>
  )
}
