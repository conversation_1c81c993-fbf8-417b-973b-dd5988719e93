'use client'

import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { logOut } from '@/lib/auth'
import { useRouter } from 'next/navigation'
import { 
  BookOpenIcon, 
  PlayIcon, 
  DocumentTextIcon, 
  AcademicCapIcon,
  ArrowRightOnRectangleIcon 
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

export default function StudentDashboard() {
  const { user } = useAuth()
  const router = useRouter()

  const handleLogout = async () => {
    const { error } = await logOut()
    if (error) {
      toast.error('حدث خطأ أثناء تسجيل الخروج')
    } else {
      toast.success('تم تسجيل الخروج بنجاح')
      router.push('/')
    }
  }

  return (
    <ProtectedRoute requiredRole="student">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div>
                <h1 className="text-2xl font-bold text-primary-900">
                  لوحة تحكم الطالب
                </h1>
                <p className="text-secondary-600">
                  مرحباً {user?.full_name || user?.displayName}
                </p>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center gap-2 px-4 py-2 text-secondary-600 hover:text-primary-600 transition-colors"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5" />
                تسجيل الخروج
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <BookOpenIcon className="h-8 w-8 text-primary-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-secondary-600">الكورسات المسجلة</p>
                  <p className="text-2xl font-bold text-primary-900">0</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <PlayIcon className="h-8 w-8 text-green-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-secondary-600">الفيديوهات المشاهدة</p>
                  <p className="text-2xl font-bold text-green-900">0</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <DocumentTextIcon className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-secondary-600">الاختبارات المكتملة</p>
                  <p className="text-2xl font-bold text-blue-900">0</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <AcademicCapIcon className="h-8 w-8 text-yellow-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-secondary-600">الشهادات المحصلة</p>
                  <p className="text-2xl font-bold text-yellow-900">0</p>
                </div>
              </div>
            </div>
          </div>

          {/* My Courses Section */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-primary-900">كورساتي</h2>
            </div>
            <div className="p-6">
              <div className="text-center py-12">
                <BookOpenIcon className="mx-auto h-12 w-12 text-secondary-400" />
                <h3 className="mt-2 text-sm font-medium text-secondary-900">
                  لا توجد كورسات مسجلة
                </h3>
                <p className="mt-1 text-sm text-secondary-500">
                  سيتم عرض الكورسات المسجلة لك هنا
                </p>
              </div>
            </div>
          </div>

          {/* Recent Activity Section */}
          <div className="mt-8 bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-primary-900">النشاط الأخير</h2>
            </div>
            <div className="p-6">
              <div className="text-center py-12">
                <div className="mx-auto h-12 w-12 bg-secondary-100 rounded-full flex items-center justify-center">
                  <PlayIcon className="h-6 w-6 text-secondary-400" />
                </div>
                <h3 className="mt-2 text-sm font-medium text-secondary-900">
                  لا يوجد نشاط حديث
                </h3>
                <p className="mt-1 text-sm text-secondary-500">
                  ابدأ بمشاهدة الكورسات لرؤية نشاطك هنا
                </p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
