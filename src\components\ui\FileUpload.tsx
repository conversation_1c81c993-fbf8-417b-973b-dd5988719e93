'use client'

import React, { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { CloudArrowUpIcon, DocumentIcon, VideoCameraIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { clsx } from 'clsx'

interface FileUploadProps {
  onFileSelect: (files: File[]) => void
  accept?: Record<string, string[]>
  maxFiles?: number
  maxSize?: number
  multiple?: boolean
  className?: string
  label?: string
  description?: string
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFileSelect,
  accept = {
    'video/*': ['.mp4', '.webm', '.ogg'],
    'application/pdf': ['.pdf'],
    'image/*': ['.png', '.jpg', '.jpeg', '.webp']
  },
  maxFiles = 1,
  maxSize = 100 * 1024 * 1024, // 100MB
  multiple = false,
  className,
  label,
  description
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setSelectedFiles(acceptedFiles)
    onFileSelect(acceptedFiles)
  }, [onFileSelect])

  const { getRootProps, getInputProps, isDragActive, fileRejections } = useDropzone({
    onDrop,
    accept,
    maxFiles,
    maxSize,
    multiple
  })

  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index)
    setSelectedFiles(newFiles)
    onFileSelect(newFiles)
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('video/')) {
      return <VideoCameraIcon className="h-8 w-8 text-blue-500" />
    } else if (file.type === 'application/pdf') {
      return <DocumentIcon className="h-8 w-8 text-red-500" />
    } else {
      return <DocumentIcon className="h-8 w-8 text-gray-500" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium text-secondary-700 mb-2">
          {label}
        </label>
      )}
      
      <div
        {...getRootProps()}
        className={clsx(
          'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
          isDragActive 
            ? 'border-primary-400 bg-primary-50' 
            : 'border-secondary-300 hover:border-primary-400 hover:bg-primary-50'
        )}
      >
        <input {...getInputProps()} />
        
        <CloudArrowUpIcon className="mx-auto h-12 w-12 text-secondary-400" />
        
        <div className="mt-4">
          <p className="text-sm font-medium text-secondary-900">
            {isDragActive ? 'اسحب الملفات هنا' : 'اسحب الملفات هنا أو انقر للاختيار'}
          </p>
          {description && (
            <p className="text-xs text-secondary-500 mt-1">
              {description}
            </p>
          )}
          <p className="text-xs text-secondary-500 mt-1">
            الحد الأقصى: {formatFileSize(maxSize)}
          </p>
        </div>
      </div>

      {/* File Rejections */}
      {fileRejections.length > 0 && (
        <div className="mt-2">
          {fileRejections.map(({ file, errors }) => (
            <div key={file.name} className="text-sm text-red-600">
              {file.name}: {errors.map(e => e.message).join(', ')}
            </div>
          ))}
        </div>
      )}

      {/* Selected Files */}
      {selectedFiles.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium text-secondary-900">الملفات المحددة:</h4>
          {selectedFiles.map((file, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
              <div className="flex items-center space-x-3 space-x-reverse">
                {getFileIcon(file)}
                <div>
                  <p className="text-sm font-medium text-secondary-900">{file.name}</p>
                  <p className="text-xs text-secondary-500">{formatFileSize(file.size)}</p>
                </div>
              </div>
              <button
                onClick={() => removeFile(index)}
                className="text-secondary-400 hover:text-red-500 transition-colors"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default FileUpload
