'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  ArrowLeftIcon,
  PlusIcon,
  TrashIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

interface QuizQuestion {
  id: string
  question: string
  options: string[]
  correctAnswer: number
  explanation?: string
}

interface QuizFormData {
  title: string
  description: string
  courseId: string
  passingScore: number
  questions: QuizQuestion[]
}

// Mock courses data
const mockCourses = [
  { id: '1', title: 'مقدمة في البرمجة' },
  { id: '2', title: 'تطوير المواقع الإلكترونية' },
  { id: '3', title: 'تصميم واجهات المستخدم' }
]

export default function CreateQuizPage() {
  const [formData, setFormData] = useState<QuizFormData>({
    title: '',
    description: '',
    courseId: '',
    passingScore: 70,
    questions: []
  })
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: name === 'passingScore' ? parseInt(value) : value
    }))
  }

  const addQuestion = () => {
    const newQuestion: QuizQuestion = {
      id: Date.now().toString(),
      question: '',
      options: ['', '', '', ''],
      correctAnswer: 0,
      explanation: ''
    }
    setFormData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }))
  }

  const updateQuestion = (questionId: string, field: keyof QuizQuestion, value: any) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map(q => 
        q.id === questionId ? { ...q, [field]: value } : q
      )
    }))
  }

  const updateQuestionOption = (questionId: string, optionIndex: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map(q => 
        q.id === questionId 
          ? { ...q, options: q.options.map((opt, idx) => idx === optionIndex ? value : opt) }
          : q
      )
    }))
  }

  const removeQuestion = (questionId: string) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.filter(q => q.id !== questionId)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validation
      if (!formData.title.trim()) {
        toast.error('يرجى إدخال عنوان الاختبار')
        return
      }

      if (!formData.courseId) {
        toast.error('يرجى اختيار الكورس')
        return
      }

      if (formData.questions.length === 0) {
        toast.error('يرجى إضافة سؤال واحد على الأقل')
        return
      }

      // Validate questions
      for (const question of formData.questions) {
        if (!question.question.trim()) {
          toast.error('يرجى إدخال نص السؤال')
          return
        }
        
        if (question.options.some(opt => !opt.trim())) {
          toast.error('يرجى إدخال جميع الخيارات')
          return
        }
      }

      // TODO: Implement quiz creation logic
      console.log('Creating quiz:', formData)
      
      toast.success('تم إنشاء الاختبار بنجاح!')
      router.push('/admin/dashboard')
      
    } catch (error) {
      toast.error('حدث خطأ أثناء إنشاء الاختبار')
    } finally {
      setLoading(false)
    }
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="mr-4"
              >
                <ArrowLeftIcon className="h-4 w-4 ml-2" />
                رجوع
              </Button>
              <h1 className="text-2xl font-bold text-primary-900">
                إنشاء اختبار جديد
              </h1>
            </div>
            <p className="text-secondary-600">
              أنشئ اختباراً لتقييم فهم الطلاب للمحتوى التعليمي
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Quiz Info */}
            <Card>
              <Card.Header>
                <Card.Title>معلومات الاختبار</Card.Title>
              </Card.Header>
              <Card.Content className="space-y-4">
                <Input
                  label="عنوان الاختبار *"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="أدخل عنوان الاختبار"
                  required
                />

                <div>
                  <label className="form-label">وصف الاختبار</label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                    className="form-input"
                    placeholder="أدخل وصفاً للاختبار (اختياري)"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="form-label">الكورس *</label>
                    <select
                      name="courseId"
                      value={formData.courseId}
                      onChange={handleInputChange}
                      className="form-input"
                      required
                    >
                      <option value="">اختر الكورس</option>
                      {mockCourses.map((course) => (
                        <option key={course.id} value={course.id}>
                          {course.title}
                        </option>
                      ))}
                    </select>
                  </div>

                  <Input
                    label="درجة النجاح (%)"
                    name="passingScore"
                    type="number"
                    min="1"
                    max="100"
                    value={formData.passingScore}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </Card.Content>
            </Card>

            {/* Questions */}
            <Card>
              <Card.Header>
                <div className="flex justify-between items-center">
                  <Card.Title>أسئلة الاختبار</Card.Title>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addQuestion}
                  >
                    <PlusIcon className="h-4 w-4 ml-2" />
                    إضافة سؤال
                  </Button>
                </div>
              </Card.Header>
              <Card.Content>
                {formData.questions.length > 0 ? (
                  <div className="space-y-6">
                    {formData.questions.map((question, index) => (
                      <div key={question.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-4">
                          <h4 className="font-medium text-primary-900">
                            السؤال {index + 1}
                          </h4>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeQuestion(question.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="space-y-4">
                          <div>
                            <label className="form-label">نص السؤال *</label>
                            <textarea
                              value={question.question}
                              onChange={(e) => updateQuestion(question.id, 'question', e.target.value)}
                              rows={2}
                              className="form-input"
                              placeholder="أدخل نص السؤال"
                              required
                            />
                          </div>

                          <div>
                            <label className="form-label">الخيارات *</label>
                            <div className="space-y-2">
                              {question.options.map((option, optionIndex) => (
                                <div key={optionIndex} className="flex items-center gap-3">
                                  <input
                                    type="radio"
                                    name={`correct-${question.id}`}
                                    checked={question.correctAnswer === optionIndex}
                                    onChange={() => updateQuestion(question.id, 'correctAnswer', optionIndex)}
                                    className="text-primary-600 focus:ring-primary-500"
                                  />
                                  <input
                                    type="text"
                                    value={option}
                                    onChange={(e) => updateQuestionOption(question.id, optionIndex, e.target.value)}
                                    className="form-input flex-1"
                                    placeholder={`الخيار ${optionIndex + 1}`}
                                    required
                                  />
                                </div>
                              ))}
                            </div>
                            <p className="text-xs text-secondary-500 mt-1">
                              اختر الإجابة الصحيحة بالنقر على الدائرة
                            </p>
                          </div>

                          <div>
                            <label className="form-label">شرح الإجابة (اختياري)</label>
                            <textarea
                              value={question.explanation || ''}
                              onChange={(e) => updateQuestion(question.id, 'explanation', e.target.value)}
                              rows={2}
                              className="form-input"
                              placeholder="أدخل شرحاً للإجابة الصحيحة"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-secondary-400" />
                    <h3 className="text-lg font-medium text-secondary-900 mb-2">
                      لا توجد أسئلة
                    </h3>
                    <p className="text-secondary-500 mb-4">
                      ابدأ بإضافة أسئلة للاختبار
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addQuestion}
                    >
                      <PlusIcon className="h-4 w-4 ml-2" />
                      إضافة سؤال
                    </Button>
                  </div>
                )}
              </Card.Content>
            </Card>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4 space-x-reverse">
              <Button
                type="button"
                variant="secondary"
                onClick={() => router.back()}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                loading={loading}
                disabled={formData.questions.length === 0}
              >
                إنشاء الاختبار
              </Button>
            </div>
          </form>
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
