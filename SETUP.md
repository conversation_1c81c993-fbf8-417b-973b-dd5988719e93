# دليل الإعداد السريع - منصة الكورسات الإلكترونية

## الخطوات الأساسية للإعداد

### 1. إعداد Firebase

#### إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إضافة مشروع" أو "Add project"
3. أدخل اسم المشروع (مثل: online-courses-platform)
4. اختر إعدادات Google Analytics (اختياري)
5. انق<PERSON> على "إنشاء مشروع"

#### تفعيل Authentication
1. في لوحة تحكم Firebase، اذهب إلى "Authentication"
2. انقر على "البدء" أو "Get started"
3. اذهب إلى تبويب "Sign-in method"
4. فعّل "Email/Password"

#### تفعيل Firestore Database
1. اذهب إلى "Firestore Database"
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية
4. اختر موقع قاعدة البيانات (أقرب منطقة لك)

#### الحصول على إعدادات Firebase
1. اذهب إلى "Project Settings" (أيقونة الترس)
2. انتقل إلى تبويب "General"
3. في قسم "Your apps"، انقر على "Add app" واختر "Web"
4. أدخل اسم التطبيق وانقر على "Register app"
5. انسخ إعدادات Firebase Config

### 2. إعداد Supabase

#### إنشاء مشروع Supabase
1. اذهب إلى [Supabase](https://supabase.com/)
2. انقر على "Start your project"
3. سجل دخول أو أنشئ حساب جديد
4. انقر على "New project"
5. اختر Organization أو أنشئ واحدة جديدة
6. أدخل اسم المشروع وكلمة مرور قاعدة البيانات
7. اختر المنطقة الأقرب لك
8. انقر على "Create new project"

#### الحصول على إعدادات Supabase
1. بعد إنشاء المشروع، اذهب إلى "Settings"
2. انقر على "API"
3. انسخ:
   - Project URL
   - Project API keys (anon/public key)
   - Service Role key (سري - لا تشاركه)

### 3. إعداد المشروع محلياً

#### تثبيت التبعيات
```bash
npm install
```

#### إعداد متغيرات البيئة
1. انسخ ملف `.env.local.example` إلى `.env.local`:
```bash
cp .env.local.example .env.local
```

2. افتح `.env.local` وأضف القيم الصحيحة:

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyC...
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abc123

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### إعداد قاعدة البيانات
```bash
npm run setup-db
```

#### تشغيل المشروع
```bash
npm run dev
```

### 4. اختبار المشروع

1. افتح المتصفح واذهب إلى `http://localhost:3000`
2. جرب إنشاء حساب جديد كطالب
3. جرب إنشاء حساب جديد كمدرس
4. تأكد من عمل تسجيل الدخول والخروج

### 5. النشر على Firebase Hosting

#### تثبيت Firebase CLI
```bash
npm install -g firebase-tools
```

#### تسجيل الدخول
```bash
firebase login
```

#### تهيئة Hosting
```bash
firebase init hosting
```

اختر:
- Use an existing project: اختر مشروعك
- Public directory: `out`
- Configure as SPA: `Yes`
- Set up automatic builds: `No`

#### النشر
```bash
npm run deploy
```

## نصائح مهمة

### الأمان
- لا تشارك Service Role Key أبداً
- استخدم متغيرات البيئة للمفاتيح السرية
- فعّل قواعد الأمان في Firestore وSupabase

### الأداء
- استخدم CDN للملفات الثابتة
- ضغط الصور قبل الرفع
- تفعيل التخزين المؤقت

### المراقبة
- راقب استخدام Firebase وSupabase
- تابع الأخطاء في Console
- استخدم Analytics لتتبع الاستخدام

## حل المشاكل الشائعة

### خطأ في الاتصال بFirebase
- تأكد من صحة إعدادات Firebase
- تحقق من تفعيل Authentication وFirestore

### خطأ في الاتصال بSupabase
- تأكد من صحة URL وAPI Keys
- تحقق من إعدادات CORS في Supabase

### مشاكل في البناء
- تأكد من تثبيت جميع التبعيات
- تحقق من عدم وجود أخطاء TypeScript

### مشاكل في النشر
- تأكد من بناء المشروع بنجاح
- تحقق من إعدادات Firebase Hosting

## الدعم

إذا واجهت أي مشاكل:
1. راجع الوثائق الرسمية لFirebase وSupabase
2. تحقق من Console للأخطاء
3. ابحث في GitHub Issues
4. اطلب المساعدة في المجتمع
