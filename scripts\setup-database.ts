import { supabase } from '../src/lib/supabase'
import { readFileSync } from 'fs'
import { join } from 'path'

async function setupDatabase() {
  try {
    console.log('🚀 Setting up database...')
    
    // Read the SQL schema file
    const schemaPath = join(process.cwd(), 'database', 'supabase-schema.sql')
    const schema = readFileSync(schemaPath, 'utf8')
    
    // Split the schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`📝 Executing ${statements.length} SQL statements...`)
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';'
      console.log(`⏳ Executing statement ${i + 1}/${statements.length}`)
      
      const { error } = await supabase.rpc('exec_sql', { sql: statement })
      
      if (error) {
        console.error(`❌ Error executing statement ${i + 1}:`, error)
        console.error('Statement:', statement)
      } else {
        console.log(`✅ Statement ${i + 1} executed successfully`)
      }
    }
    
    // Create storage buckets
    console.log('📁 Setting up storage buckets...')
    
    const buckets = [
      { name: 'course-videos', public: false },
      { name: 'course-materials', public: false },
      { name: 'certificates', public: false },
      { name: 'avatars', public: true },
      { name: 'course-thumbnails', public: true }
    ]
    
    for (const bucket of buckets) {
      const { error } = await supabase.storage.createBucket(bucket.name, {
        public: bucket.public,
        allowedMimeTypes: bucket.name === 'course-videos' 
          ? ['video/mp4', 'video/webm', 'video/ogg']
          : bucket.name === 'course-materials'
          ? ['application/pdf']
          : bucket.name === 'certificates'
          ? ['application/pdf', 'image/png', 'image/jpeg']
          : ['image/png', 'image/jpeg', 'image/webp']
      })
      
      if (error && !error.message.includes('already exists')) {
        console.error(`❌ Error creating bucket ${bucket.name}:`, error)
      } else {
        console.log(`✅ Bucket ${bucket.name} ready`)
      }
    }
    
    // Set up storage policies
    console.log('🔒 Setting up storage policies...')
    
    const storagePolicies = [
      // Course videos - only instructors can upload, enrolled students can view
      {
        bucket: 'course-videos',
        policy: 'course_videos_upload',
        definition: `
          CREATE POLICY "Instructors can upload course videos" ON storage.objects
          FOR INSERT WITH CHECK (
            bucket_id = 'course-videos' AND
            EXISTS (
              SELECT 1 FROM public.users 
              WHERE id = auth.uid() AND role = 'admin'
            )
          );
        `
      },
      {
        bucket: 'course-videos',
        policy: 'course_videos_view',
        definition: `
          CREATE POLICY "Enrolled students can view course videos" ON storage.objects
          FOR SELECT USING (
            bucket_id = 'course-videos' AND (
              EXISTS (
                SELECT 1 FROM public.users 
                WHERE id = auth.uid() AND role = 'admin'
              ) OR
              EXISTS (
                SELECT 1 FROM public.student_courses sc
                JOIN public.videos v ON v.video_url LIKE '%' || name || '%'
                JOIN public.course_sections cs ON cs.id = v.section_id
                WHERE sc.student_id = auth.uid() AND sc.course_id = cs.course_id
              )
            )
          );
        `
      }
    ]
    
    for (const policy of storagePolicies) {
      const { error } = await supabase.rpc('exec_sql', { sql: policy.definition })
      
      if (error && !error.message.includes('already exists')) {
        console.error(`❌ Error creating storage policy ${policy.policy}:`, error)
      } else {
        console.log(`✅ Storage policy ${policy.policy} ready`)
      }
    }
    
    console.log('🎉 Database setup completed successfully!')
    
  } catch (error) {
    console.error('❌ Database setup failed:', error)
    process.exit(1)
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupDatabase()
}

export default setupDatabase
