'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Modal from '@/components/ui/Modal'
import FileUpload from '@/components/ui/FileUpload'
import { 
  ArrowLeftIcon,
  PlusIcon,
  VideoCameraIcon,
  DocumentTextIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  EyeIcon
} from '@heroicons/react/24/outline'

// Mock data - will be replaced with real data from database
const mockCourse = {
  id: '1',
  title: 'مقدمة في البرمجة',
  description: 'تعلم أساسيات البرمجة باستخدام JavaScript',
  isPublished: true,
  sections: [
    {
      id: '1',
      title: 'الأساسيات',
      orderIndex: 1,
      videos: [
        {
          id: '1',
          title: 'مقدمة عن البرمجة',
          duration: 600, // 10 minutes
          orderIndex: 1,
          pdfs: [
            { id: '1', title: 'ملاحظات الدرس الأول', fileSize: 1024000 }
          ]
        },
        {
          id: '2',
          title: 'إعداد بيئة التطوير',
          duration: 900, // 15 minutes
          orderIndex: 2,
          pdfs: []
        }
      ]
    }
  ]
}

export default function CourseDetailsPage({ params }: { params: { id: string } }) {
  const [course] = useState(mockCourse)
  const [isAddSectionModalOpen, setIsAddSectionModalOpen] = useState(false)
  const [isAddVideoModalOpen, setIsAddVideoModalOpen] = useState(false)
  const [isAddPdfModalOpen, setIsAddPdfModalOpen] = useState(false)
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(null)
  const [selectedVideoId, setSelectedVideoId] = useState<string | null>(null)
  const [newSectionTitle, setNewSectionTitle] = useState('')
  const [newVideoTitle, setNewVideoTitle] = useState('')
  const [selectedVideoFile, setSelectedVideoFile] = useState<File | null>(null)
  const [selectedPdfFiles, setSelectedPdfFiles] = useState<File[]>([])
  const router = useRouter()

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleAddSection = () => {
    if (newSectionTitle.trim()) {
      // TODO: Implement section creation logic
      console.log('Adding section:', newSectionTitle)
      setIsAddSectionModalOpen(false)
      setNewSectionTitle('')
    }
  }

  const handleAddVideo = () => {
    if (newVideoTitle.trim() && selectedVideoFile && selectedSectionId) {
      // TODO: Implement video upload logic
      console.log('Adding video:', newVideoTitle, 'to section:', selectedSectionId)
      setIsAddVideoModalOpen(false)
      setNewVideoTitle('')
      setSelectedVideoFile(null)
      setSelectedSectionId(null)
    }
  }

  const handleAddPdf = () => {
    if (selectedPdfFiles.length > 0 && selectedVideoId) {
      // TODO: Implement PDF upload logic
      console.log('Adding PDFs to video:', selectedVideoId)
      setIsAddPdfModalOpen(false)
      setSelectedPdfFiles([])
      setSelectedVideoId(null)
    }
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="mr-4"
              >
                <ArrowLeftIcon className="h-4 w-4 ml-2" />
                رجوع
              </Button>
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-primary-900">
                  {course.title}
                </h1>
                <p className="text-secondary-600">
                  {course.description}
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline">
                  <PencilIcon className="h-4 w-4 ml-2" />
                  تعديل الكورس
                </Button>
                <Button
                  onClick={() => setIsAddSectionModalOpen(true)}
                >
                  <PlusIcon className="h-4 w-4 ml-2" />
                  إضافة قسم
                </Button>
              </div>
            </div>
          </div>

          {/* Course Sections */}
          <div className="space-y-6">
            {course.sections.map((section) => (
              <Card key={section.id}>
                <Card.Header>
                  <div className="flex justify-between items-center">
                    <Card.Title>{section.title}</Card.Title>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedSectionId(section.id)
                        setIsAddVideoModalOpen(true)
                      }}
                    >
                      <PlusIcon className="h-4 w-4 ml-1" />
                      إضافة فيديو
                    </Button>
                  </div>
                </Card.Header>

                <Card.Content>
                  {section.videos.length > 0 ? (
                    <div className="space-y-4">
                      {section.videos.map((video) => (
                        <div key={video.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <VideoCameraIcon className="h-5 w-5 text-blue-600" />
                              </div>
                              <div>
                                <h4 className="font-medium text-primary-900">
                                  {video.title}
                                </h4>
                                <p className="text-sm text-secondary-500">
                                  المدة: {formatDuration(video.duration)}
                                </p>
                              </div>
                            </div>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm">
                                <PlayIcon className="h-4 w-4 ml-1" />
                                تشغيل
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedVideoId(video.id)
                                  setIsAddPdfModalOpen(true)
                                }}
                              >
                                <PlusIcon className="h-4 w-4 ml-1" />
                                إضافة PDF
                              </Button>
                            </div>
                          </div>

                          {/* PDFs */}
                          {video.pdfs.length > 0 && (
                            <div className="mt-3 pt-3 border-t">
                              <h5 className="text-sm font-medium text-secondary-700 mb-2">
                                المواد التعليمية:
                              </h5>
                              <div className="space-y-2">
                                {video.pdfs.map((pdf) => (
                                  <div key={pdf.id} className="flex items-center justify-between bg-secondary-50 p-2 rounded">
                                    <div className="flex items-center gap-2">
                                      <DocumentTextIcon className="h-4 w-4 text-red-500" />
                                      <span className="text-sm">{pdf.title}</span>
                                      <span className="text-xs text-secondary-500">
                                        ({formatFileSize(pdf.fileSize)})
                                      </span>
                                    </div>
                                    <div className="flex gap-1">
                                      <Button variant="ghost" size="sm">
                                        <EyeIcon className="h-3 w-3" />
                                      </Button>
                                      <Button variant="ghost" size="sm" className="text-red-600">
                                        <TrashIcon className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-secondary-500">
                      <VideoCameraIcon className="h-8 w-8 mx-auto mb-2 text-secondary-400" />
                      <p>لا توجد فيديوهات في هذا القسم</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() => {
                          setSelectedSectionId(section.id)
                          setIsAddVideoModalOpen(true)
                        }}
                      >
                        إضافة فيديو
                      </Button>
                    </div>
                  )}
                </Card.Content>
              </Card>
            ))}

            {course.sections.length === 0 && (
              <Card>
                <div className="text-center py-12">
                  <VideoCameraIcon className="h-12 w-12 mx-auto mb-4 text-secondary-400" />
                  <h3 className="text-lg font-medium text-secondary-900 mb-2">
                    لا توجد أقسام في هذا الكورس
                  </h3>
                  <p className="text-secondary-500 mb-4">
                    ابدأ بإضافة قسم جديد لتنظيم محتوى الكورس
                  </p>
                  <Button onClick={() => setIsAddSectionModalOpen(true)}>
                    <PlusIcon className="h-4 w-4 ml-2" />
                    إضافة قسم جديد
                  </Button>
                </div>
              </Card>
            )}
          </div>
        </div>

        {/* Add Section Modal */}
        <Modal
          isOpen={isAddSectionModalOpen}
          onClose={() => {
            setIsAddSectionModalOpen(false)
            setNewSectionTitle('')
          }}
          title="إضافة قسم جديد"
        >
          <div className="space-y-4">
            <div>
              <label className="form-label">عنوان القسم</label>
              <input
                type="text"
                value={newSectionTitle}
                onChange={(e) => setNewSectionTitle(e.target.value)}
                className="form-input"
                placeholder="أدخل عنوان القسم"
              />
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => {
                  setIsAddSectionModalOpen(false)
                  setNewSectionTitle('')
                }}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleAddSection}
                disabled={!newSectionTitle.trim()}
              >
                إضافة القسم
              </Button>
            </div>
          </div>
        </Modal>

        {/* Add Video Modal */}
        <Modal
          isOpen={isAddVideoModalOpen}
          onClose={() => {
            setIsAddVideoModalOpen(false)
            setNewVideoTitle('')
            setSelectedVideoFile(null)
            setSelectedSectionId(null)
          }}
          title="إضافة فيديو جديد"
          size="lg"
        >
          <div className="space-y-4">
            <div>
              <label className="form-label">عنوان الفيديو</label>
              <input
                type="text"
                value={newVideoTitle}
                onChange={(e) => setNewVideoTitle(e.target.value)}
                className="form-input"
                placeholder="أدخل عنوان الفيديو"
              />
            </div>

            <FileUpload
              label="ملف الفيديو"
              description="اختر ملف فيديو (MP4, WebM, OGG - حتى 100MB)"
              accept={{
                'video/*': ['.mp4', '.webm', '.ogg']
              }}
              maxSize={100 * 1024 * 1024} // 100MB
              onFileSelect={(files) => setSelectedVideoFile(files[0] || null)}
            />

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => {
                  setIsAddVideoModalOpen(false)
                  setNewVideoTitle('')
                  setSelectedVideoFile(null)
                  setSelectedSectionId(null)
                }}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleAddVideo}
                disabled={!newVideoTitle.trim() || !selectedVideoFile}
              >
                رفع الفيديو
              </Button>
            </div>
          </div>
        </Modal>

        {/* Add PDF Modal */}
        <Modal
          isOpen={isAddPdfModalOpen}
          onClose={() => {
            setIsAddPdfModalOpen(false)
            setSelectedPdfFiles([])
            setSelectedVideoId(null)
          }}
          title="إضافة مواد تعليمية (PDF)"
        >
          <div className="space-y-4">
            <FileUpload
              label="ملفات PDF"
              description="اختر ملفات PDF كمواد تعليمية مساعدة (حتى 10MB لكل ملف)"
              accept={{
                'application/pdf': ['.pdf']
              }}
              maxSize={10 * 1024 * 1024} // 10MB
              multiple={true}
              maxFiles={5}
              onFileSelect={setSelectedPdfFiles}
            />

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => {
                  setIsAddPdfModalOpen(false)
                  setSelectedPdfFiles([])
                  setSelectedVideoId(null)
                }}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleAddPdf}
                disabled={selectedPdfFiles.length === 0}
              >
                رفع الملفات
              </Button>
            </div>
          </div>
        </Modal>
      </Layout>
    </ProtectedRoute>
  )
}
