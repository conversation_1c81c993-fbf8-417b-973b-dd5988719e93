'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { logOut } from '@/lib/auth'
import { useRouter } from 'next/navigation'
import { 
  Bars3Icon, 
  XMarkIcon, 
  UserIcon,
  ArrowRightOnRectangleIcon,
  HomeIcon,
  BookOpenIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { user, isAdmin, isStudent } = useAuth()
  const router = useRouter()

  const handleLogout = async () => {
    const { error } = await logOut()
    if (error) {
      toast.error('حدث خطأ أثناء تسجيل الخروج')
    } else {
      toast.success('تم تسجيل الخروج بنجاح')
      router.push('/')
    }
  }

  const adminNavItems = [
    { name: 'الرئيسية', href: '/admin/dashboard', icon: HomeIcon },
    { name: 'الكورسات', href: '/admin/courses', icon: BookOpenIcon },
    { name: 'الطلاب', href: '/admin/students', icon: UserIcon },
    { name: 'الشهادات', href: '/admin/certificates', icon: AcademicCapIcon },
  ]

  const studentNavItems = [
    { name: 'الرئيسية', href: '/student/dashboard', icon: HomeIcon },
    { name: 'كورساتي', href: '/student/courses', icon: BookOpenIcon },
    { name: 'شهاداتي', href: '/student/certificates', icon: AcademicCapIcon },
  ]

  const navItems = isAdmin ? adminNavItems : isStudent ? studentNavItems : []

  return (
    <nav className="bg-white shadow-sm border-b border-secondary-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <div className="flex-shrink-0">
                <BookOpenIcon className="h-8 w-8 text-primary-600" />
              </div>
              <div className="mr-3">
                <span className="text-xl font-bold text-primary-900">
                  منصة الكورسات
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          {user && (
            <div className="hidden md:flex items-center space-x-8 space-x-reverse">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="flex items-center px-3 py-2 text-sm font-medium text-secondary-700 hover:text-primary-600 transition-colors"
                  >
                    <Icon className="h-4 w-4 ml-2" />
                    {item.name}
                  </Link>
                )
              })}
            </div>
          )}

          {/* User Menu */}
          <div className="flex items-center">
            {user ? (
              <div className="flex items-center space-x-4 space-x-reverse">
                <span className="text-sm text-secondary-700">
                  مرحباً، {user.full_name || user.displayName}
                </span>
                <button
                  onClick={handleLogout}
                  className="flex items-center px-3 py-2 text-sm font-medium text-secondary-700 hover:text-primary-600 transition-colors"
                >
                  <ArrowRightOnRectangleIcon className="h-4 w-4 ml-2" />
                  خروج
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-4 space-x-reverse">
                <Link
                  href="/auth/login?role=student"
                  className="text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"
                >
                  دخول الطلاب
                </Link>
                <Link
                  href="/auth/login?role=admin"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  دخول المدرسين
                </Link>
              </div>
            )}

            {/* Mobile menu button */}
            {user && (
              <div className="md:hidden mr-4">
                <button
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                  className="text-secondary-700 hover:text-primary-600 p-2"
                >
                  {isMenuOpen ? (
                    <XMarkIcon className="h-6 w-6" />
                  ) : (
                    <Bars3Icon className="h-6 w-6" />
                  )}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Navigation */}
        {user && isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 border-t border-secondary-200">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="flex items-center px-3 py-2 text-base font-medium text-secondary-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Icon className="h-5 w-5 ml-3" />
                    {item.name}
                  </Link>
                )
              })}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Navbar
